/* eslint-disable no-console */
import {
  SINK_BRAND_TLD_CONTACT,
  SINK_BRAND_TLD_SEARCH,
  SINK_CLOUD,
  SINK_HIBP,
  SINK_IMPERSONATION,
  SINK_NDS,
  SINK_PASSWORD_CONFIGURATION,
  SINK_PASSWORD_CONTACT,
  SINK_POST,
  SINK_POST_FEEDBACK,
  SINK_SENDGRID_EVENT,
  SINK_SITE_RISK_CONFIGURATION,
  SINK_SITE_RISK_CONTACT,
  SINK_SSL,
} from '../constants/sinks.js';
import { hashEmail } from './cryptography.js';
import { convertDate } from './daytime.js';
import { toSnakeCaseObject } from './string.js';

export const createHibpSink = async ({ email, createdAt, expiredAt, code, result, isRegularly, name, isIncludedName }) => {
  try {
    const log = {
      sink: SINK_HIBP,
      code,
      created_at: createdAt,
      expired_at: expiredAt,
      hashed_email: await hashEmail(email),
      names: result.filter(r => r.DataClasses.includes('Passwords')).map(r => r.Name),
      count: result.filter(r => r.DataClasses.includes('Passwords')).length,
      is_regularly: isRegularly,
      is_emergency: !!name,
      name,
      is_included_name: isIncludedName,
    };

    console.log(JSON.stringify(log));
    return log;
  } catch (err) {
    console.error(new Error(`Create Hibp Sink Error email: ${email}`));
    console.error(err);
  }
};

export const createNdsSink = async ({ email, fqdn, createdAt, expiredAt, code, scanId, rank, status, result, isRegularly, cve, isIncludedCve }) => {
  try {
    const levelCounts = (result.items ?? [])
      .map((r) => {
        let level = 'info';
        if (r.cvss >= 9.5) level = 'critical';
        else if (r.cvss >= 8) level = 'high';
        else if (r.cvss >= 5.5) level = 'medium';
        else if (r.cvss >= 2) level = 'low';
        return level;
      }).reduce((pre, cur) => {
        pre[cur]++;
        return pre;
      }, { info: 0, low: 0, medium: 0, high: 0, critical: 0 });

    const log = {
      sink: SINK_NDS,
      code,
      created_at: createdAt,
      expired_at: expiredAt,
      hashed_email: await hashEmail(email),
      fqdn,
      status: status,
      rank: rank,
      scan_id: scanId,
      info: levelCounts['info'],
      low: levelCounts['low'],
      medium: levelCounts['medium'],
      high: levelCounts['high'],
      critical: levelCounts['critical'],
      is_regularly: isRegularly,
      is_emergency: !!cve,
      cve,
      is_included_cve: isIncludedCve,
      titles: (result.items ?? []).filter(({ cvss }) => cvss >= 5.5).map(({ title }) => title),
    };

    console.log(JSON.stringify(log));
    return log;
  } catch (err) {
    console.error(new Error(`Create Nds Sink Error fqdn: ${fqdn}`));
    console.error(err);
  }
};

export const createCloudSink = async (email, fqdn, createdAt, expiredAt, code, { status, result }, isRegularly) => {
  try {
    const log = {
      sink: SINK_CLOUD,
      code,
      created_at: createdAt,
      expired_at: expiredAt,
      hashed_email: await hashEmail(email),
      fqdn,
      status: status,
      providers: Array.from(new Set(result.filter(r => !!r.provider).map(r => r.provider))),
      is_regularly: isRegularly,
    };

    console.log(JSON.stringify(log));
    return log;
  } catch (err) {
    console.error(new Error(`Create Cloud Sink Error fqdn: ${fqdn}`));
    console.error(err);
  }
};

export const createSslSink = async (email, fqdn, createdAt, expiredAt, code, { status, result }, isRegularly) => {
  try {
    const log = {
      sink: SINK_SSL,
      code,
      created_at: createdAt,
      expired_at: expiredAt,
      hashed_email: await hashEmail(email),
      fqdn,
      status: status,
      hostname_verification: result.map(({ hostname_verification }) => !!hostname_verification && !!hostname_verification.verified).reduce((pre, cur) => pre && cur, true),
      cert_verification: result.map(({ cert_verification }) => !!cert_verification && !!cert_verification.verified).reduce((pre, cur) => pre && cur, true),
      free_ssl_provider: result.find(({ free_ssl_provider }) => !!free_ssl_provider)?.free_ssl_provider ?? null,
      earliest_expires: result.filter(({ validity }) => !!validity && !!validity.not_after).map(({ validity }) => convertDate(validity.not_after)).sort((a, b) => a.getTime() - b.getTime())[0] ?? null,
      is_regularly: isRegularly,
    };

    console.log(JSON.stringify(log));
    return log;
  } catch (err) {
    console.error(new Error(`Create Ssl Sink Error fqdn: ${fqdn}`));
    console.error(err);
  }
};

export const createImpersonationSink = async (email, fqdn, createdAt, expiredAt, code, { status, result }, isRegularly) => {
  try {
    const log = {
      sink: SINK_IMPERSONATION,
      code,
      created_at: createdAt,
      expired_at: expiredAt,
      hashed_email: await hashEmail(email),
      fqdn,
      status: status,
      rank: result.rank,
      count: result.czds.count,
      bimi: result.bimi,
      brand_tld: result.brand_tld,
      vmc: !!result.vmc,
      is_regularly: isRegularly,
      spf: result.spf,
      dmarc: !!result.dmarc,
    };

    console.log(JSON.stringify(log));
    return log;
  } catch (err) {
    console.error(new Error(`Create Impersonation Sink Error fqdn: ${fqdn}`));
    console.error(err);
  }
};

export const createPostSink = (threadTs, response, messages, post, reply, path) => {
  try {
    const log = {
      sink: SINK_POST,
      thread_ts: threadTs,
      ts: response.ts,
      reply_number: messages.filter(({ role }) => role === 'assistant').length,
      post,
      reply,
      path,
    };

    console.log(JSON.stringify(log));
    return log;
  } catch (err) {
    console.error(err);
  }
};

export const createPostFeedbackSink = (threadTs, replyNumber, feedback) => {
  try {
    const log = {
      sink: SINK_POST_FEEDBACK,
      thread_ts: threadTs,
      reply_number: replyNumber,
      feedback,
    };

    console.log(JSON.stringify(log));
    return log;
  } catch (err) {
    console.error(err);
  }
};

export const createPasswordContactSink = async (email, code, consultation) => {
  try {
    const log = {
      sink: SINK_PASSWORD_CONTACT,
      code,
      hashed_email: await hashEmail(email),
      consultation,
    };

    console.log(JSON.stringify(log));
    return log;
  } catch (err) {
    console.error(err);
  }
};

export const createSiteRiskContactSink = async (email, code, fqdn, targets, consultation, threadTs) => {
  try {
    const log = {
      sink: SINK_SITE_RISK_CONTACT,
      code,
      hashed_email: await hashEmail(email),
      fqdn,
      targets,
      consultation,
      thread_ts: threadTs,
    };

    console.log(JSON.stringify(log));
    return log;
  } catch (err) {
    console.error(err);
  }
};

export const createBrandTldContactSink = async ({ email, tld }) => {
  try {
    const log = {
      sink: SINK_BRAND_TLD_CONTACT,
      hashed_email: await hashEmail(email),
      tld,
    };

    console.log(JSON.stringify(log));
    return log;
  } catch (err) {
    console.error(err);
  }
};

export const createBrandTldSearchSink = async ({ tld, available }) => {
  try {
    const log = toSnakeCaseObject({
      sink: SINK_BRAND_TLD_SEARCH,
      tld,
      available,
    });

    console.log(JSON.stringify(log));
    return log;
  } catch (err) {
    console.error(err);
  }
};

export const createPasswordConfigurationSink = async (email, isRegularly, interval, nextCheckedAt) => {
  try {
    const log = {
      sink: SINK_PASSWORD_CONFIGURATION,
      hashed_email: await hashEmail(email),
      is_regularly: isRegularly,
      interval,
      next_checked_at: nextCheckedAt ? nextCheckedAt.toISOString() : null,
    };

    console.log(JSON.stringify(log));
    return log;
  } catch (err) {
    console.error(err);
  }
};

export const createSiteRiskConfigurationSink = async (email, fqdn, isRegularly, interval, nextCheckedAt) => {
  try {
    const log = {
      sink: SINK_SITE_RISK_CONFIGURATION,
      hashed_email: await hashEmail(email),
      fqdn,
      is_regularly: isRegularly,
      interval,
      next_checked_at: nextCheckedAt ? nextCheckedAt.toISOString() : null,
    };

    console.log(JSON.stringify(log));
    return log;
  } catch (err) {
    console.error(err);
  }
};

export const createSendgridEventSink = async ({ email, sentAt, ...rest }) => {
  try {
    const log = {
      sink: SINK_SENDGRID_EVENT,
      hashed_email: await hashEmail(email),
      sent_at: sentAt.toISOString(),
      ...toSnakeCaseObject(rest),
    };

    console.log(JSON.stringify(log));
    return log;
  } catch (err) {
    console.error(err);
  }
};
