import { PubSub } from '@google-cloud/pubsub';
import { COLLECTION_NDS } from '../constants/collections.js';
import { CVE_NAMES } from '../constants/cves.js';
import { SLACK_REPLACEMENT_TAMPLATE_NDS, SLACK_TEMPLATE_NDS_RESULT } from '../constants/slack_template.js';
import { TEMPLATE_EMERGENCY_NDS_READY, TEMPLATE_NDS_READY, TEMPLATE_REGULARLY_NDS_READY } from '../constants/template.js';
import { TOPIC_SEND_EMAIL, TOPIC_SEND_SLACK_MESSAGE, TOPIC_WRITE_SITE_RISK_HISTORY } from '../constants/topics.js';
import firestore from '../providers/firestore.js';
import { createNdsSink } from '../services/sink.js';

const schema = {
  body: {
    type: 'object',
    required: ['id', 'fqdn', 'rank', 'result'],
    properties: {
      id: { type: 'integer' },
      fqdn: { type: 'string' },
      rank: { type: 'string' },
      result: { type: 'object' },
    },
  },
};

const pubSubClient = new PubSub();

const handler = async (request, reply) => {
  const { id, fqdn, rank, result, ...rest } = request.body;

  const snapshot = await firestore.collection(COLLECTION_NDS).where('scanId', '==', `${id}`).get();
  if (snapshot.empty) {
    console.error(`Nds record not found scanId: ${id}`);
    return reply.send('1');
  }
  if (snapshot.size > 1) {
    console.error(`Nds record found multiple scanId: ${id}`);
    return reply.send('1');
  }
  const doc = snapshot.docs[0];
  const code = doc.id;
  const data = doc.data();
  const createdAt = data.createdAt ?? (new Date()).toISOString();
  await doc.ref.update({ createdAt, result: { rank, result, ...rest } });

  let isIncludedCve = false;
  if (data.cve) {
    isIncludedCve = result.items.map(i => includesCve(i.metadata, data.cve)).reduce((pre, cur) => pre || cur, false);
  }

  const log = await createNdsSink({ email: data.email, fqdn, createdAt, expiredAt: data.expiredAt, code, scanId: id, rank, result, ...rest, isRegularly: data.isRegularly ?? false, cve: data.cve, isIncludedCve });

  let topic = pubSubClient.topic(TOPIC_WRITE_SITE_RISK_HISTORY);
  let message = Buffer.from(JSON.stringify({ email: data.email, fqdn, result: { nds: log } }));
  await topic.publishMessage({ data: message });

  topic = pubSubClient.topic(TOPIC_SEND_SLACK_MESSAGE);

  message = Buffer.from(JSON.stringify({ token: process.env.SECRET_SLACK_USER_TOKEN, channelId: process.env.SLACK_FQDN_CHANNEL_ID, message: { template: SLACK_TEMPLATE_NDS_RESULT, params: log }, threadTs: data.threadTs ?? null }));
  await topic.publishMessage({ data: message });

  message = Buffer.from(JSON.stringify({ token: process.env.SECRET_SLACK_USER_TOKEN, channelId: process.env.SLACK_FQDN_CHANNEL_ID, threadTs: data.threadTs ?? null, updateOptions: [{ template: SLACK_REPLACEMENT_TAMPLATE_NDS, params: log }] }));
  await topic.publishMessage({ data: message });

  topic = pubSubClient.topic(TOPIC_SEND_EMAIL);
  let url = `${process.env.HOST}${process.env.PATH_PREFIX}/check/site-risk/?code=${code}`;

  let template = TEMPLATE_NDS_READY;
  let cveDetail;
  if (data.cve) {
    template = TEMPLATE_EMERGENCY_NDS_READY;
    url += `&cve=${data.cve}`;
    cveDetail = CVE_NAMES[cve];
    if (!isIncludesCve) url = undefined;
  }
  if (data.isRegularly) {
    template = TEMPLATE_REGULARLY_NDS_READY;
    url += `&is_regularly=1`;
  }

  message = Buffer.from(JSON.stringify({ email: data.email, template, params: { createdAt, expiredAt: data.expiredAt, fqdn, url, cve: data.cve, cveDetail } }));
  await topic.publishMessage({ data: message });

  return reply.send('1');
};

const includesCve = (item, cve) => {
  if (item === null) return false;
  if (typeof item !== 'object') return false;

  return Object.keys(item).map((k) => {
    if (k === 'vuln_info') {
      return Object.values(item[k]).map(v => v.includes(cve)).reduce((pre, cur) => pre || cur, false);
    }
    return includesCve(item[k], cve);
  }).reduce((pre, cur) => pre || cur, false);
};

export default { schema, handler };
