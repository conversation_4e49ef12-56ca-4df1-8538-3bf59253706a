import { PubSub } from '@google-cloud/pubsub';
import { decrypt } from '../services/cryptography.js';
import { isVerified } from '../services/recaptcha.js';
import { publishCheckFqdn } from './post_fqdn.js';

const schema = {
  body: {
    type: 'object',
    required: ['code', 'recaptchaToken'],
    properties: {
      code: { type: 'string' },
      recaptchaToken: { type: 'string' },
    },
  },
};

const pubSubClient = new PubSub();

const handler = async (request, reply) => {
  const { code, recaptchaToken } = request.body;

  if (!(await isVerified(recaptchaToken))) {
    return reply.status(400).send({ status: 'error', message: 'Invalid reCAPTCHA token' });
  }

  const decrypted = await decrypt(code, process.env.SECRET_CRYPTOGRAPHY_PASSWORD, process.env.SECRET_CRYPTOGRAPHY_SALT);
  if (!decrypted) {
    return reply.status(400).send({ status: 'error', message: 'Invalid code' });
  }

  const { email, fqdn, expiredAt } = JSON.parse(decrypted);
  if (!email || !fqdn || !expiredAt) {
    return reply.status(400).send({ status: 'error', message: 'Invalid code' });
  }
  const now = new Date();
  if (expiredAt < now.getTime()) {
    return reply.status(400).send({ status: 'error', message: 'Code is expired' });
  }

  await publishCheckFqdn(email, fqdn);

  return reply.send({ status: 'success', result: { email, fqdn } });
};

export default { schema, handler };
