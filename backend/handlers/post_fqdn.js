import { PubSub } from '@google-cloud/pubsub';
import { isCompanyEmail } from 'free-email-domains-list';
import { CONFIRM_EXIPRES_MILLISECOND, CONSTANT_DNS_TXT_RECORD_KEY, TTL_MILLISECOND } from '../constants/constants.js';
import { generateCheckFqdnMessage } from '../constants/slack_template.js';
import { TEMPLATE_CONFIRM_EMAIL } from '../constants/template.js';
import { TOPIC_CHECK_FQDN, TOPIC_SEND_EMAIL } from '../constants/topics.js';
import { encrypt, hash } from '../services/cryptography.js';
import { isVerified as isFqdnVerified } from '../services/dns.js';
import { isVerified } from '../services/recaptcha.js';
import { sendMessage } from '../services/slack.js';

const schema = {
  body: {
    type: 'object',
    required: ['fqdn', 'email', 'recaptchaToken'],
    properties: {
      fqdn: {
        type: 'string',
        pattern: '^(?=.{1,255}$)([A-Za-z0-9\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FFF](?:[A-Za-z0-9\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FFF-]{0,61}[A-Za-z0-9\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FFF])?\.)+[A-Za-z\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FFF]{2,}$',
      },
      email: {
        type: 'string',
        format: 'email',
      },
      recaptchaToken: { type: 'string' },
    },
  },
};

const pubSubClient = new PubSub({ projectId: process.env.PROJECT_ID });

export const isDomainMatch = (email, fqdn) => {
  const emailDomain = email.split('@')[1];

  if (emailDomain === fqdn) {
    return true;
  }

  const emailDomainParts = emailDomain.split('.').reverse();
  const fqdnParts = fqdn.split('.').reverse();

  for (let i = 0; i < emailDomainParts.length; i++) {
    if (emailDomainParts[i] !== fqdnParts[i]) {
      return false;
    }
  }

  return true;
};

export const publishCheckFqdn = async (email, fqdn, isRegularly = false, cve) => {
  const createdAt = new Date();
  const expiredAt = new Date(createdAt.getTime() + TTL_MILLISECOND);

  const code = await encrypt(
    JSON.stringify({ email, fqdn, expiredAt: expiredAt.getTime() }),
    process.env.SECRET_CRYPTOGRAPHY_PASSWORD,
    process.env.SECRET_CRYPTOGRAPHY_SALT);

  const fqdnMessage = generateCheckFqdnMessage(fqdn, code, isRegularly);

  const { ts } = await sendMessage(process.env.SECRET_SLACK_USER_TOKEN, process.env.SLACK_FQDN_CHANNEL_ID, fqdnMessage);

  const topic = pubSubClient.topic(TOPIC_CHECK_FQDN);
  const message = Buffer.from(JSON.stringify({ email, fqdn, createdAt, expiredAt, code, threadTs: ts, isRegularly, cve }));

  await topic.publishMessage({ data: message });
};

const handler = async (request, reply) => {
  const { fqdn, email, recaptchaToken } = request.body;

  if (!(await isVerified(recaptchaToken))) {
    return reply.status(400).send({ status: 'error', message: 'Invalid reCAPTCHA token' });
  }

  if (isCompanyEmail(email) && isDomainMatch(email, fqdn)) {
    const now = new Date();
    const expiredAt = new Date(now.getTime() + CONFIRM_EXIPRES_MILLISECOND);

    const code = await encrypt(
      JSON.stringify({ email, fqdn, expiredAt: expiredAt.getTime() }),
      process.env.SECRET_CRYPTOGRAPHY_PASSWORD,
      process.env.SECRET_CRYPTOGRAPHY_SALT);

    const topic = pubSubClient.topic(TOPIC_SEND_EMAIL);
    const url = `${process.env.HOST}${process.env.PATH_PREFIX}/check/confirm/?code=${code}`;
    const message = Buffer.from(JSON.stringify({ email, template: TEMPLATE_CONFIRM_EMAIL, params: { expiredAt, fqdn, url } }));
    await topic.publishMessage({ data: message });

    return reply.send({ status: 'confirm' });
  }

  const authTxt = await hash(`${email}:${fqdn}`);
  if (!(await isFqdnVerified(fqdn, CONSTANT_DNS_TXT_RECORD_KEY, authTxt))) {
    return reply.status(200).send({ status: 'request', result: { authTxt } });
  }

  await publishCheckFqdn(email, fqdn);

  return reply.send({ status: 'success' });
};

export default { schema, handler };
