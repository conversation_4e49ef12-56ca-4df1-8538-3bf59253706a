import { PubSub } from '@google-cloud/pubsub';
import { COLLECTION_HIBP } from '../constants/collections.js';
import { HIBP_NAMES } from '../constants/hibps.js';
import { TEMPLATE_EMERGENCY_HIBP_READY, TEMPLATE_HIBP_READY, TEMPLATE_REGULARLY_HIBP_READY } from '../constants/template.js';
import { TOPIC_SEND_EMAIL, TOPIC_WRITE_PASSWORD_HISTORY } from '../constants/topics.js';
import { docExists, saveDoc } from '../providers/firestore.js';
import { request } from '../services/hibp.js';
import { createHibpSink } from '../services/sink.js';

const pubSubClient = new PubSub();

const subscriber = async ({ data }) => {
  const { email, createdAt, expiredAt, code, isRegularly = false, name } = JSON.parse(Buffer.from(data.message.data, 'base64').toString());
  if (!email || !createdAt || !expiredAt || !code) {
    console.error(new Error('email, createdAt, expiredAt and code are required'));
    return;
  }

  if (await docExists({ collection: COLLECTION_HIBP, docId: code })) {
    console.error(new Error(`hibp record dupricated code: ${code}`));
    return;
  }

  const result = await request(email);
  if (!result) return;

  let isIncludedName = null;
  if (name) {
    isIncludedName = result.filter(r => r.DataClasses.includes('Passwords')).map(r => r.Name).includes(name);
  }
  const { names, count } = await createHibpSink({ email, createdAt, expiredAt, code, result, isRegularly, name, isIncludedName });

  const dataHibp = { email, createdAt, expiredAt, result, isRegularly };
  await saveDoc({ collection: COLLECTION_HIBP, docId: code, data: dataHibp });

  if (!name || isIncludedName) {
    let topic = pubSubClient.topic(TOPIC_WRITE_PASSWORD_HISTORY);
    let message = Buffer.from(JSON.stringify({ email, createdAt, result: { code, names, count } }));

    await topic.publishMessage({ data: message });

    topic = pubSubClient.topic(TOPIC_SEND_EMAIL);
    let url = `${process.env.HOST}${process.env.PATH_PREFIX}/check/password/?code=${code}`;
    let template = TEMPLATE_HIBP_READY;
    let title;
    if (name) {
      template = TEMPLATE_EMERGENCY_HIBP_READY;
      url += `&hname=${name}`;
      title = HIBP_NAMES[name]?.title ?? name;
    }
    if (isRegularly) {
      template = TEMPLATE_REGULARLY_HIBP_READY;
      url += '&is_regularly=1';
    }
    message = Buffer.from(JSON.stringify({ email, template, params: { createdAt, expiredAt, url, title } }));

    await topic.publishMessage({ data: message });
  }
};

export default subscriber;
