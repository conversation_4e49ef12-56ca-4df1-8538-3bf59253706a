import cors from '@fastify/cors';
import Ajv from 'ajv';
import addFormats from 'ajv-formats';
import Fastify from 'fastify';
import getPassword from '../handlers/get_password.js';
import getSiteRisk from '../handlers/get_site_risk.js';
import getTld from '../handlers/get_tld.js';
import postChat from '../handlers/post_chat.js';
import postConfiguration from '../handlers/post_configuration.js';
import postConfirm from '../handlers/post_confirm.js';
import postContact from '../handlers/post_contact.js';
import postEmail from '../handlers/post_email.js';
import postFeedback from '../handlers/post_feedback.js';
import postFqdn from '../handlers/post_fqdn.js';
import postReceiveNds from '../handlers/post_receive_nds.js';
import postReceiveSendgrid from '../handlers/post_receive_sendgrid.js';

const fastify = Fastify({ logger: true });

fastify.register(cors, { origin: '*' });

fastify.addHook('onRequest', (request, reply, done) => {
  const cfConnectingIp = request.headers['cf-connecting-ip'] || '';
  const xCloudTraceContext = request.headers['x-cloud-trace-context'];

  let globalLogFields = {};

  if (xCloudTraceContext) {
    const [trace] = xCloudTraceContext.split('/');
    globalLogFields['logging.googleapis.com/trace']
      = `projects/${process.env.PROJECT_ID}/traces/${trace}`;
  }

  const entry = Object.assign(
    {
      severity: 'NOTICE',
      cfConnectingIp,
    },
    globalLogFields,
  );
  // eslint-disable-next-line no-console
  console.log(JSON.stringify(entry));

  done();
});

fastify.addContentTypeParser('application/json', {}, (req, body, done) => {
  done(null, body.body);
});

fastify.setErrorHandler((error, request, reply) => {
  let globalLogFields = {};

  const xCloudTraceContext = request.headers['x-cloud-trace-context'];
  if (xCloudTraceContext) {
    const [trace] = xCloudTraceContext.split('/');
    globalLogFields['logging.googleapis.com/trace']
      = `projects/${process.env.PROJECT_ID}/traces/${trace}`;
  }

  if (error.code === 'FST_ERR_VALIDATION') {
    const entry = Object.assign(
      {
        severity: 'WARNING',
        error,
      },
      globalLogFields,
    );
    // eslint-disable-next-line no-console
    console.log(JSON.stringify(entry));
    reply.status(400).send({ status: 'error', message: 'Validation Error' });
  } else {
    const entry = Object.assign(
      {
        severity: 'ERROR',
        error: error.message,
      },
      globalLogFields,
    );
    // eslint-disable-next-line no-console
    console.log(JSON.stringify(entry));
    console.error(error);
    reply.status(500).send({ status: 'error', message: 'Internal Server Error' });
  }
});

const ajv = new Ajv();
addFormats(ajv);
ajv.addKeyword({
  keyword: 'minBytes',
  type: 'string',
  validate: function (schema, data) {
    return typeof data === 'string' && Buffer.byteLength(data, 'utf8') >= schema;
  },
  errors: false,
});

fastify.setValidatorCompiler(({ schema }) => {
  return ajv.compile(schema);
});

fastify.get('/api/password', getPassword);
fastify.get('/api/site-risk', getSiteRisk);
fastify.get('/api/tld', getTld);

fastify.post('/api/chat', postChat);
fastify.post('/api/confirm', postConfirm);
fastify.post('/api/contact', postContact);
fastify.post('/api/email', postEmail);
fastify.post('/api/feedback', postFeedback);
fastify.post('/api/fqdn', postFqdn);

fastify.post('/api/configuration', postConfiguration);

fastify.post('/webhook/nds', postReceiveNds);
fastify.post('/webhook/sendgrid', postReceiveSendgrid);

const server = async (request, reply) => {
  await fastify.ready();
  fastify.server.emit('request', request, reply);
};

export default server;
