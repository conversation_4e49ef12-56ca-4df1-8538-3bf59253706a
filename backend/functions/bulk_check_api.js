import { PubSub } from '@google-cloud/pubsub';
import { COLLECTION_REGULARLY_PASSWORD_CONFIGURATION, COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION } from '../constants/collections.js';
import { TOPIC_PRE_CHECK_EMAIL, TOPIC_PRE_CHECK_FQDN } from '../constants/topics.js';
import { getDoc, getDocsByNextCheckedAt } from '../providers/firestore.js';

const pubSubClient = new PubSub();

const api = async (req, res) => {
  try {
    const { collection, isRegularly = false, cve = null, name = null, preview = true } = req.body;
    if (!collection) {
      throw new Error('collection is required');
    }
    if (![COLLECTION_REGULARLY_PASSWORD_CONFIGURATION, COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION].includes(collection)) {
      throw new Error(`collection is ${COLLECTION_REGULARLY_PASSWORD_CONFIGURATION} or ${COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION}`);
    }
    if (!isRegularly && !(!!cve || !!name)) {
      throw new Error('isRegularly = true or cve/name is required');
    }

    let to = new Date();
    if (!isRegularly) {
      to = new Date('2999-12-31 23:59:59'); // 定期診断ではない場合、全件が対象のため
    }

    let reservationDocs;
    if (preview) {
      if (collection === COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION) {
        reservationDocs = [await getDoc({ collection, docId: process.env.PREVIEW_SITE_RISK_CONFIGURATION_ID })];
      } else {
        reservationDocs = [await getDoc({ collection, docId: process.env.PREVIEW_PASSWORD_CONFIGURATION_ID })];
      }
    } else {
      reservationDocs = await getDocsByNextCheckedAt({ collection, to: to.toISOString() });
    }

    let topic = pubSubClient.topic(TOPIC_PRE_CHECK_EMAIL);
    if (collection === COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION) {
      topic = pubSubClient.topic(TOPIC_PRE_CHECK_FQDN);
    }

    for (const doc of reservationDocs) {
      const { encryptedEmail, isRegularly: docIsRegularly, interval, ...rest } = doc.data();
      if (!docIsRegularly) {
        console.error(new Error(`Configured isRegularly = false id: ${doc.id}`));
        continue;
      }

      const nextCheckedAt = new Date();
      nextCheckedAt.setMonth(nextCheckedAt.getMonth() + interval);
      doc.ref.update({ nextCheckedAt: nextCheckedAt.toISOString() });

      const data = Buffer.from(JSON.stringify({ encryptedEmail, isRegularly, ...rest, cve, name }));
      await topic.publishMessage({ data });
    }

    if (preview) {
      res.send('PREVIEW OK');
    } else {
      res.send('OK');
    }
  } catch (err) {
    console.error(err);
    res.status(500).send('ERROR');
  }
};

export default api;
