import {
  generateAnnounceRegularly,
  generateConfirm<PERSON>mailContent,
  generateHib<PERSON><PERSON>ontent,
  generateNdsContent,
  generatePass<PERSON>ContactThanksContent,
  generateRegularlyHibpContent,
  generateRegularlyNdsContent,
  generateNdsContactThanksContent,
  generateCloudContactThanksContent,
  generateImpersonationContactThanksContent,
  generateSiteRiskContactThanksContent,
  generateSslContactThanksContent,
  generateBrandTldContactThanksContent,
  TEMPLATE_CONFIRM_EMAIL,
  TEMPLATE_HIBP_READY,
  TEMPLATE_IMPERSONATION_READY,
  TEMPLATE_NDS_READY,
  TEMPLATE_PASSWORD_CONTACT_THANKS,
  TEMPLATE_RECON_READY,
  TEMPLATE_REGULARLY_ANNOUNCE,
  TEMPLATE_REGULARLY_HIBP_READY,
  TEMPLATE_REGULARLY_IMPERSONATION_READY,
  TEMPLATE_REGULARLY_NDS_READY,
  TEMPLATE_REGULARLY_RECON_READY,
  TEMPLATE_SITE_RISK_CONTACT_THANKS,
  TEMPLATE_NDS_CONTACT_THANKS,
  TEMPLATE_CLOUD_CONTACT_THANKS,
  TEMPLATE_IMPERSONATION_CONTACT_THANKS,
  TEMPLATE_SSL_CONTACT_THANKS,
  TEMPLATE_BRAND_TLD_CONTACT_THANKS,
  TEMPLATE_EMERGENCY_HIBP_READY,
  TEMPLATE_EMERGENCY_NDS_READY,
  generateEmergencyHibpContent,
} from '../constants/template.js';
import { convertUTCtoJST } from '../services/daytime.js';
import { request } from '../services/sendgrid.js';

const subscriber = async ({ data }) => {
  const { email, template, params } = JSON.parse(Buffer.from(data.message.data, 'base64').toString());

  if (!email) {
    console.error(new Error('email is required'));
    return;
  }
  if (!template) {
    console.error(new Error('template is required'));
    return;
  }
  if (!params) {
    console.error(new Error('params is required'));
    return;
  }

  let subject, content;
  let customArgs = { template, env: process.env.ENV };
  switch (template) {
    case TEMPLATE_CONFIRM_EMAIL: {
      if (!params.url) {
        console.error(new Error('params.url is required'));
        return;
      }
      if (!params.expiredAt) {
        console.error(new Error('params.expiredAt is required'));
        return;
      }

      const expiredAtJST = convertUTCtoJST(params.expiredAt);

      subject = `【GMOセキュリティ24】サイトリスク診断 本人確認メール（${params.fqdn}）`;
      content = generateConfirmEmailContent(expiredAtJST, params.fqdn, params.url);
      customArgs.fqdn = params.fqdn;
      break;
    }
    case TEMPLATE_HIBP_READY: {
      if (!params.url) {
        console.error(new Error('params.url is required'));
        return;
      }
      if (!params.createdAt) {
        console.error(new Error('params.createdAt is required'));
        return;
      }
      if (!params.expiredAt) {
        console.error(new Error('params.expiredAt is required'));
        return;
      }

      const createdAtJST = convertUTCtoJST(params.createdAt);
      const expiredAtJST = convertUTCtoJST(params.expiredAt);

      const regex = new RegExp(`[?&]code=([^&]*)`);
      const matches = params.url.match(regex);
      customArgs.code = matches ? matches[1] : null;

      subject = `【GMOセキュリティ24】パスワード漏洩診断 結果報告（${email}）`;
      content = generateHibpContent(createdAtJST, expiredAtJST, email, params.url);
      break;
    }
    case TEMPLATE_IMPERSONATION_READY:
    case TEMPLATE_NDS_READY:
    case TEMPLATE_RECON_READY: {
      if (!params.fqdn) {
        console.error(new Error('params.fqdn is required'));
        return;
      }
      if (!params.url) {
        console.error(new Error('params.url is required'));
        return;
      }
      if (!params.createdAt) {
        console.error(new Error('params.createdAt is required'));
        return;
      }
      if (!params.expiredAt) {
        console.error(new Error('params.expiredAt is required'));
        return;
      }

      const createdAtJST = convertUTCtoJST(params.createdAt);
      const expiredAtJST = convertUTCtoJST(params.expiredAt);
      customArgs.fqdn = params.fqdn;

      const regex = new RegExp(`[?&]code=([^&]*)`);
      const matches = params.url.match(regex);
      customArgs.code = matches ? matches[1] : null;

      subject = `【GMOセキュリティ24】サイトリスク診断 結果報告（${params.fqdn}）`;
      content = generateNdsContent(createdAtJST, expiredAtJST, params.fqdn, params.url);
      break;
    }
    case TEMPLATE_REGULARLY_HIBP_READY: {
      if (!params.url) {
        console.error(new Error('params.url is required'));
        return;
      }
      if (!params.createdAt) {
        console.error(new Error('params.createdAt is required'));
        return;
      }
      if (!params.expiredAt) {
        console.error(new Error('params.expiredAt is required'));
        return;
      }

      const createdAtJST = convertUTCtoJST(params.createdAt);
      const expiredAtJST = convertUTCtoJST(params.expiredAt);

      const regex = new RegExp(`[?&]code=([^&]*)`);
      const matches = params.url.match(regex);
      customArgs.code = matches ? matches[1] : null;

      subject = `【GMOセキュリティ24】【定期診断】パスワード漏洩診断 結果報告（${email}）`;
      content = generateRegularlyHibpContent(createdAtJST, expiredAtJST, email, params.url);
      break;
    }
    case TEMPLATE_REGULARLY_IMPERSONATION_READY:
    case TEMPLATE_REGULARLY_NDS_READY:
    case TEMPLATE_REGULARLY_RECON_READY: {
      if (!params.fqdn) {
        console.error(new Error('params.fqdn is required'));
        return;
      }
      if (!params.url) {
        console.error(new Error('params.url is required'));
        return;
      }
      if (!params.createdAt) {
        console.error(new Error('params.createdAt is required'));
        return;
      }
      if (!params.expiredAt) {
        console.error(new Error('params.expiredAt is required'));
        return;
      }

      const createdAtJST = convertUTCtoJST(params.createdAt);
      const expiredAtJST = convertUTCtoJST(params.expiredAt);
      customArgs.fqdn = params.fqdn;

      const regex = new RegExp(`[?&]code=([^&]*)`);
      const matches = params.url.match(regex);
      customArgs.code = matches ? matches[1] : null;

      subject = `【GMOセキュリティ24】【定期診断】サイトリスク診断 結果報告（${params.fqdn}）`;
      content = generateRegularlyNdsContent(createdAtJST, expiredAtJST, params.fqdn, params.url);
      break;
    }
    case TEMPLATE_EMERGENCY_HIBP_READY: {
      if (!params.url) {
        console.error(new Error('params.url is required'));
        return;
      }
      if (!params.createdAt) {
        console.error(new Error('params.createdAt is required'));
        return;
      }
      if (!params.expiredAt) {
        console.error(new Error('params.expiredAt is required'));
        return;
      }
      if (!params.title) {
        console.error(new Error('params.title is required'));
        return;
      }

      const createdAtJST = convertUTCtoJST(params.createdAt);
      const expiredAtJST = convertUTCtoJST(params.expiredAt);

      const regex = new RegExp(`[?&]code=([^&]*)`);
      const matches = params.url.match(regex);
      customArgs.code = matches ? matches[1] : null;

      subject = `【GMOセキュリティ24】【緊急診断】パスワード漏洩診断 結果報告（${email}）`;
      content = generateEmergencyHibpContent({ createdAt: createdAtJST, expiredAt: expiredAtJST, email, url: params.url, title: params.title });
      break;
    }
    case TEMPLATE_EMERGENCY_NDS_READY: {
      if (!params.fqdn) {
        console.error(new Error('params.fqdn is required'));
        return;
      }
      if (!params.cve) {
        console.error(new Error('params.cve is required'));
        return;
      }
      if (!params.cveDetail) {
        console.error(new Error('params.cveDetail is required'));
        return;
      }

      customArgs.fqdn = params.fqdn;

      if (!params.url) {
        subject = `【GMOセキュリティ24】【緊急診断】サイトリスク診断 結果報告（${params.fqdn}）`;
        content = generateEmergencyyNdsContentToSafetyFqdn({ fqdn: params.fqdn, cve: params.cve, ...cveDetail });
      } else {
        if (!params.createdAt) {
          console.error(new Error('params.createdAt is required'));
          return;
        }
        if (!params.expiredAt) {
          console.error(new Error('params.expiredAt is required'));
          return;
        }
        const createdAtJST = convertUTCtoJST(params.createdAt);
        const expiredAtJST = convertUTCtoJST(params.expiredAt);

        const regex = new RegExp(`[?&]code=([^&]*)`);
        const matches = params.url.match(regex);
        customArgs.code = matches ? matches[1] : null;

        subject = `【GMOセキュリティ24】【緊急診断】サイトリスク診断 結果報告（${params.fqdn}）`;
        content = generateEmergencyyNdsContentToUnsafetyFqdn({
          createdAt: createdAtJST,
          expiredAt: expiredAtJST,
          fqdn: params.fqdn,
          url: params.url,
          cve: params.cve,
          ...cveDetail,
        });
      }
      break;
    }
    case TEMPLATE_PASSWORD_CONTACT_THANKS: {
      if (!params.contactedAt) {
        console.error(new Error('params.contactedAt is required'));
        return;
      }

      const contactedAtJST = convertUTCtoJST(params.contactedAt);

      subject = `【GMOセキュリティ24】お問い合わせ受付のお知らせ​`;
      content = generatePasswordContactThanksContent(params.consultation, contactedAtJST);
      break;
    }
    case TEMPLATE_NDS_CONTACT_THANKS: {
      subject = `【GMOセキュリティ24】お問い合わせ受付のお知らせ​`;
      content = generateNdsContactThanksContent();
      break;
    }
    case TEMPLATE_CLOUD_CONTACT_THANKS: {
      subject = `【GMOセキュリティ24】お問い合わせ受付のお知らせ​`;
      content = generateCloudContactThanksContent();
      break;
    }
    case TEMPLATE_SSL_CONTACT_THANKS: {
      subject = `【GMOセキュリティ24】お問い合わせ受付のお知らせ​`;
      content = generateSslContactThanksContent();
      break;
    }
    case TEMPLATE_IMPERSONATION_CONTACT_THANKS: {
      subject = `【GMOセキュリティ24】お問い合わせ受付のお知らせ​`;
      content = generateImpersonationContactThanksContent();
      break;
    }
    case TEMPLATE_SITE_RISK_CONTACT_THANKS: {
      if (!params.targets || params.targets.length === 0) {
        console.error(new Error('params.targets is required'));
        return;
      }
      if (!params.contactedAt) {
        console.error(new Error('params.contactedAt is required'));
        return;
      }
      const contactedAtJST = convertUTCtoJST(params.contactedAt);

      subject = `【GMOセキュリティ24】お問い合わせ受付のお知らせ​`;
      content = generateSiteRiskContactThanksContent(params.targets, params.consultation, contactedAtJST);
      break;
    }
    case TEMPLATE_BRAND_TLD_CONTACT_THANKS: {
      subject = `【GMO『.貴社名』申請・運用支援サービス】お問い合わせ受付のお知らせ`;
      content = generateBrandTldContactThanksContent(params.fullname);
      break;
    }
    case TEMPLATE_REGULARLY_ANNOUNCE: {
      subject = `【GMOセキュリティ24】定期診断機能リリースのお知らせ [設定推奨]`;
      content = generateAnnounceRegularly(params.password, params.siteRisks);
      break;
    }
    default:
      console.error(new Error(`Unhandling template: ${template}`));
      return;
  }

  const personalizations = [{ to: [{ email }], subject }];

  await request({ personalizations, content, customArgs });
};

export default subscriber;
