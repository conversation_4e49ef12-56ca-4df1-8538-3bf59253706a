import { isCompanyEmail } from 'free-email-domains-list';
import { CONSTANT_DNS_TXT_RECORD_KEY } from '../constants/constants.js';
import { isDomainMatch, publishCheckFqdn } from '../handlers/post_fqdn.js';
import { hash, priDecrypt } from '../services/cryptography.js';
import { isVerified } from '../services/dns.js';

const subscriber = async ({ data }) => {
  const { encryptedEmail, fqdn, isRegularly = false, cve } = JSON.parse(Buffer.from(data.message.data, 'base64').toString());
  if (!encryptedEmail) {
    console.error(new Error('encryptedEmail is required'));
    return;
  }

  const email = await priDecrypt(encryptedEmail, process.env.SECRET_PRIVATE_KEY);
  const authTxt = await hash(`${email}:${fqdn}`);
  if (!(isCompanyEmail(email) && isDomainMatch(email, fqdn)) && !(await isVerified(fqdn, CONSTANT_DNS_TXT_RECORD_KEY, authTxt))) {
    console.error(new Error(`Unverify fqdn id: ${authTxt}`));
    return;
  }

  await publishCheckFqdn(email, fqdn, isRegularly, cve);
};

export default subscriber;
