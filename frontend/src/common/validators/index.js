import Ajv from 'ajv';
import ajvErrors from 'ajv-errors';
import addFormats from 'ajv-formats';
import ajvKeywords from 'ajv-keywords';

const getByteLength = (str) => {
  if (str == null) return 0;
  const encoder = new TextEncoder();
  const encodedData = encoder.encode(str);
  return encodedData.length;
};

const ajv = new Ajv({ allErrors: true });
addFormats(ajv, ['email', 'hostname']);
ajvErrors(ajv);
ajvKeywords(ajv, ['transform']);

ajv.addKeyword({
  keyword: 'minBytes',
  type: 'string',
  validate: function (schema, data) {
    return typeof data === 'string' && getByteLength(data) >= schema;
  },
  errors: false,
});

const emailSchema = {
  type: 'string',
  format: 'email',
  transform: ['trim'],
};
export const validateEmail = ajv.compile(emailSchema);

export const validateSslContactForm = (() => {
  const validator = new Ajv({ allErrors: true });
  ajvErrors(validator);
  ajvKeywords(validator, ['transform']);

  const schema = {
    type: 'object',
    required: ['telephone', 'firstName', 'lastName'],
    properties: {
      firstName: {
        type: 'string',
        minLength: 1,
        transform: ['trim'],
      },
      lastName: {
        type: 'string',
        minLength: 1,
        transform: ['trim'],
      },
      telephone: {
        type: 'string',
        pattern: '^(0\\d{1,4}-?\\d{1,4}-?\\d{3,4}|0\\d{9,10})$',
        transform: ['trim'],
      },
    },
    errorMessage: {
      properties: {
        firstName: '姓を入力してください',
        lastName: '名を入力してください',
        telephone: '正しい電話番号を入力してください',
      },
    },
  };

  return {
    validate: (data) => {
      const isValid = validator.validate(schema, data);
      return {
        isValid,
        errors: validator.errors,
      };
    },
    errors: null,
  };
})();

export const validateTld = (() => {
  const validator = new Ajv({ allErrors: true });
  ajvErrors(validator);
  ajvKeywords(validator, ['transform']);

  validator.addKeyword({
    keyword: 'minBytes',
    type: 'string',
    validate: function (schema, data) {
      return typeof data === 'string' && getByteLength(data) >= schema;
    },
    errors: false,
  });

  const schema = {
    type: 'object',
    required: ['tld'],
    properties: {
      tld: {
        type: 'string',
        minLength: 1,
        minBytes: 3,
        transform: ['trim'],
        pattern: '^[A-Za-z\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FFF]*$',
        not: { pattern: '[0-9]' },
        errorMessage: {
          minLength: '社名またはブランド名を入力してください',
          not: '数字は使用できません',
          pattern: '記号は使用できません',
          minBytes: '3文字以上で入力してください',
          _: '社名またはブランド名を入力してください',
        },
      },
    },
  };

  return {
    validate: (data) => {
      const isValid = validator.validate(schema, data);
      return { isValid, errors: validator.errors };
    },
    errors: null,
  };
})();

export const validateYourbrandContactForm = (() => {
  const validator = new Ajv({ allErrors: true });
  ajvErrors(validator);
  ajvKeywords(validator, ['transform']);

  const schema = {
    type: 'object',
    required: ['company', 'firstName', 'lastName', 'email', 'telephone'],
    properties: {
      company: {
        type: 'string',
        minLength: 1,
        transform: ['trim'],
      },
      firstName: {
        type: 'string',
        minLength: 1,
        transform: ['trim'],
      },
      lastName: {
        type: 'string',
        minLength: 1,
        transform: ['trim'],
      },
      email: {
        type: 'string',
        pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$',
        transform: ['trim'],
      },
      telephone: {
        type: 'string',
        pattern: '^(0\\d{1,4}-?\\d{1,4}-?\\d{3,4}|0\\d{9,10})$',
        transform: ['trim'],
      },
    },
    errorMessage: {
      properties: {
        company: '会社名を入力してください',
        firstName: '名を入力してください',
        lastName: '姓を入力してください',
        email: '有効なメールアドレスを入力してください',
        telephone: '正しい電話番号を入力してください',
      },
    },
  };

  return {
    validate: (data) => {
      const isValid = validator.validate(schema, data);
      return {
        isValid,
        errors: validator.errors,
      };
    },
    errors: null,
  };
})();
