import { useState, useEffect } from 'react';
import Button from '../../common/components/Button';
import useChatHandler from '../../common/hooks/useChatHandler';

import {
  isValidEmail,
  isValidFqdn,
  isValidUrl,
  getFqdn,
} from '../../common/utils';
import Header from './Header';
import SearchInput from './SearchInput';
import Tabs from './Tabs';

function Home() {
  const searchParams = new URLSearchParams(window.location.search);
  const tabParam = searchParams.get('tab');
  const defaultActiveTab = tabParam === 'chat' ? 'chat' : 'diagnostic';

  const [isLoading, setIsLoading] = useState(false);
  const [grecaptcha, setGrecaptcha] = useState(null);
  const [activeTab, setActiveTab] = useState(defaultActiveTab);
  const [diagnosticValue, setDiagnosticValue] = useState('');
  const [chatValue, setChatValue] = useState('');
  const [error, setError] = useState('');
  const { isChatValidAndAllowed, chatError } = useChatHandler();

  useEffect(() => {
    const { grecaptcha } = window;
    if (grecaptcha) {
      grecaptcha.ready(() => {
        setGrecaptcha(grecaptcha);
      });
    }
    const state = localStorage.getItem('state');
    if (state) {
      setDiagnosticValue(JSON.parse(state).diagnosticValue);
      localStorage.removeItem('state');
    }
  }, []);

  const handleDiagnosticSubmit = async (value) => {
    setIsLoading(true);

    try {
      if (!grecaptcha) {
        throw new Error('Not fount grecaptcha');
      }
      const recaptchaToken = await grecaptcha.execute(
        import.meta.env.VITE_RECAPTCHA_SITE_KEY,
        { action: 'submit' },
      );
      if (isValidEmail(value)) {
        const response = await fetch(
          `${import.meta.env.VITE_API_HOST}/api/email`,
          {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email: value, recaptchaToken }),
          },
        );

        if (response.ok) {
          localStorage.setItem('state', JSON.stringify({ email: value }));
          window.location.href = `${import.meta.env.VITE_PATH_PREFIX}/check/complete/`;
        } else {
          const { status, message } = await response.json();
          if (status === 'error') {
            throw new Error(`Email api error message: ${message}`);
          }
        }
      } else if (isValidFqdn(value) || isValidUrl(value)) {
        localStorage.setItem('state', JSON.stringify({ fqdn: getFqdn(value) }));
        window.location.href = `${import.meta.env.VITE_PATH_PREFIX}/check/email/`;
      } else {
        setError('無効なメールアドレスまたはURLです');
      }
    } catch (err) {
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleChatSubmit = async (value) => {
    if (!isChatValidAndAllowed(value)) {
      return;
    }

    localStorage.setItem('state', JSON.stringify({ content: value }));
    window.location.href = `${import.meta.env.VITE_PATH_PREFIX}/check/chat/`;
  };

  return (
    <section className="p-home">
      <div className="p-home__inner">
        <div className="p-home__header">
          <Header title="総合ネットセキュリティサービス GMOセキュリティ24" />
        </div>
        <p className="p-home__text">
          {activeTab === 'diagnostic'
            ? (
              <>
                パスワードの漏洩、WEBの侵入リスクなどを
                <span>無料でお調べいたします</span>
              </>
            )
            : (
              <>ネット利用に関するご不安にお答えいたします</>
            )}
        </p>
        <div className="p-home__search">
          {activeTab === 'diagnostic'
            ? (
              <div
                role="tabpanel"
                id="tab-panel1"
                className={
                  activeTab == 'diagnostic'
                    ? 'p-home__content p-home__content--active'
                    : 'p-home__content'
                }
                aria-labelledby="tab1"
                tabIndex={activeTab == 'diagnostic' ? '0' : '-1'}
              >
                <SearchInput
                  onSubmit={handleDiagnosticSubmit}
                  isLoading={isLoading}
                  key="searchInput-diagnostic"
                  onChange={value => setDiagnosticValue(value)}
                  initValue={diagnosticValue}
                />
                {error && (
                  <p className="p-home__error">
                    {error}
                  </p>
                )}
              </div>
            )
            : (
              <div
                role="tabpanel"
                id="tab-panel2"
                className={
                  activeTab == 'chat'
                    ? 'p-home__content p-home__content--active'
                    : 'p-home__content'
                }
                aria-labelledby="tab2"
                tabIndex={activeTab == 'chat' ? '0' : '-1'}
              >
                <SearchInput
                  onSubmit={handleChatSubmit}
                  placeholder="セキュリティの懸念を入力"
                  buttonText="相談"
                  key="searchInput-chat"
                  onChange={value => setChatValue(value)}
                  initValue={chatValue}
                  isLoading={isLoading}
                />
                {chatError && (
                  <p className="p-home__error">
                    <span className="icon-base icon-sec-caution icon-color-white" />
                    {chatError}
                  </p>
                )}
              </div>
            )}
          <div className="p-home__tabs">
            <Tabs activeTab={activeTab} onTabChange={setActiveTab} />
          </div>
        </div>
        <div className="p-home__brandTld">
          <a
            id="top_yourbrand_link"
            href="/security/yourbrand/"
            target="_blank"
            rel="noopener"
            className="p-home__brandLink"
          >
            10年に1度の取得チャンス！
            <span className="p-home__textBreak">
              「
              <span className="p-home__textEm">.貴社名</span>
              」でなりすまし対策
            </span>
          </a>
        </div>
        <div className="p-home__link">
          ※
          <Button
            as="a"
            href="https://www.gmo.jp/security/check/agreement/"
            target="_blank"
            rel="noopener"
            variant="text"
            referrerPolicy="strict-origin-when-cross-origin"
          >
            利用規約
          </Button>
          と
          <Button
            as="a"
            href="https://www.gmo.jp/csr/governance/privacy-policy/"
            target="_blank"
            rel="noopener"
            variant="text"
            referrerPolicy="strict-origin-when-cross-origin"
          >
            プライバシーポリシー
          </Button>
          をお読みいただき、同意の上で開始してください。
        </div>
      </div>
    </section>
  );
}

export default Home;
