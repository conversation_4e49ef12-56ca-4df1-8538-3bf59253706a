import PropTypes from 'prop-types';
import { Fragment, useState } from 'react';
import { ToastContainer, toast } from 'react-toastify';
import DropDownList from '../../common/components/DropDownList';
import InfoTooltip from '../../common/components/InfoTolltip';
import Snackbar from '../../common/components/Snackbar';
import ToggleSwitch from '../../common/components/ToggleSwitch';

var apiQueue = [];
const callApi = async (
  resolve,
  reject,
  code,
  isRegularly,
  interval,
  isNotification,
) => {
  apiQueue.push({
    code: code,
    isRegularly: isRegularly,
    interval: interval,
    isNotification: isNotification,
    resolve: resolve,
    reject: reject,
  });
  if (apiQueue.length > 1) {
    return;
  }
  while (apiQueue.length > 0) {
    const data = apiQueue[0];
    await _callApi(
      data.code,
      data.isRegularly,
      data.interval,
      data.isNotification,
    )
      .then(() => data.resolve())
      .catch(e => data.reject(e));
    apiQueue.shift();
  }
};

const _callApi = async (code, isRegularly, interval, isNotification) => {
  const response = await fetch(
    `${import.meta.env.VITE_API_HOST}/api/configuration`,
    {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ code, isRegularly, interval, isNotification }),
    },
  );

  if (!response.ok) {
    const { status, message } = await response.json();
    throw new Error(`Email api error ${status} message: ${message}`);
  }
};

const SiteRiskPeriodicCheckup = ({
  code,
  isRegularly,
  interval,
  isNotification,
  nextCheckedAt,
  createdAt,
}) => {
  const [enablePeriodicCheckup, setEnablePeriodicCheckup] = useState(
    isRegularly ?? false,
  );
  const enableNewsDelivery = isNotification ?? false;

  const [checkInterval, setCheckInterval] = useState(`${interval ?? 1}ヶ月毎`);

  const [nextCheckDateStr, setNextCheckDateStr] = useState(
    (new Date('2025-03-10').getTime() < new Date(nextCheckedAt).getTime()
      ? new Date(nextCheckedAt)
      : new Date('2025-03-10')
    ).toLocaleDateString('sv-SE'),
  );

  const updateNextCheckDate = (itv) => {
    if (createdAt) {
      const newDate = new Date(createdAt);
      newDate.setMonth(newDate.getMonth() + itv);
      setNextCheckDateStr(
        (new Date('2025-03-10').getTime() < newDate.getTime()
          ? newDate
          : new Date('2025-03-10')
        ).toLocaleDateString('sv-SE'),
      );
    }
  };

  const handleTogglePeriodicCheckup = () => {
    const itv = parseInt(checkInterval[0]);
    const p = new Promise((resolve, reject) => {
      callApi(
        resolve,
        reject,
        code,
        !enablePeriodicCheckup,
        itv,
        enableNewsDelivery,
      );
    });
    p.then(() => {
      toast(<Snackbar>設定が保存されました</Snackbar>);
    });
    setEnablePeriodicCheckup(!enablePeriodicCheckup);
    updateNextCheckDate(parseInt(checkInterval[0]));
  };

  const onChangeInterval = (data) => {
    const itv = parseInt(data[0]);
    const p = new Promise((resolve, reject) => {
      callApi(
        resolve,
        reject,
        code,
        enablePeriodicCheckup,
        itv,
        enableNewsDelivery,
      );
    });
    p.then(() => {
      toast(<Snackbar>設定が保存されました</Snackbar>);
    });
    setCheckInterval(data);
    updateNextCheckDate(itv);
  };

  return (
    <Fragment>
      <ToastContainer hideProgressBar={true} limit={3} />
      <div className="c-siteRiskPeriodicCheckup">
        <div className="c-siteRiskPeriodicCheckup__textWrap">
          <div className="c-siteRiskPeriodicCheckup__icon">
            <span className="icon-base icon-sec-cycle icon-size16 icon-color-darkGreen" />
          </div>
          <p className="c-siteRiskPeriodicCheckup__text">
            定期診断を無償で行い、メールでお知らせします。
          </p>
        </div>
        <div className="c-siteRiskPeriodicCheckup__list">
          <div className="c-siteRiskPeriodicCheckupElement">
            <span>定期診断</span>
            <ToggleSwitch
              name="site_risk_periodic_checkup"
              isOn={enablePeriodicCheckup}
              handleToggle={handleTogglePeriodicCheckup}
            />
            <DropDownList
              name="site_risk_periodic_dropdown"
              value={checkInterval}
              onChange={onChangeInterval}
              disabled={!enablePeriodicCheckup}
              options={['1ヶ月毎', '3ヶ月毎', '6ヶ月毎']}
            />
            <InfoTooltip
              content={`以下2点より、Webサイトを常に安全に保つためには月1回の診断を推奨します。
①2024年には「1日あたり100個以上※1」の新規脆弱性が確認されていること
②システム改修/更新により新たな脆弱性が発生する可能性があること

※1 出典：https://www.cvedetails.com/browse-by-date.php`}
            />
          </div>
        </div>
        {enablePeriodicCheckup && (
          <div className="c-siteRiskPeriodicCheckup__date">
            次回診断日：
            {enablePeriodicCheckup ? nextCheckDateStr : null}
          </div>
        )}
      </div>
    </Fragment>
  );
};

SiteRiskPeriodicCheckup.propTypes = {
  code: PropTypes.string,
  isRegularly: PropTypes.bool,
  interval: PropTypes.number,
  isNotification: PropTypes.bool,
  nextCheckedAt: PropTypes.string,
  createdAt: PropTypes.string,
};

export default SiteRiskPeriodicCheckup;
