import PropTypes from 'prop-types';
import Button from '../../../common/components/Button';
import SiteRiskPeriodicCheckup from '../SiteRiskPeriodicCheckup';
import OverviewCard from './OverviewCard';
import OverviewGrid from './OverviewGrid';

const NDS_RANKS = {
  A: '安全です',
  B: '安全です',
  C: '要対策',
  D: '要対策',
  E: '要緊急対応',
};

const IMPERSONATION_RANKS = {
  A: '安全です',
  B: '要注意',
  C: '危険',
  D: '非常に危険',
};

const NdsIcon = () => (
  <span className="icon-base icon-sec-security icon-size20 icon-color-darkGreen" />
);
const CloudIcon = () => (
  <span className="icon-base icon-sec-cloud icon-size20 icon-color-darkGreen" />
);
const SslIcon = () => (
  <span className="icon-base icon-sec-ssl icon-size20 icon-color-darkGreen" />
);
const ImpersonationIcon = () => (
  <span className="icon-base icon-sec-impersonation icon-size20 icon-color-darkGreen" />
);

function ExpiredOverview({ fqdn, nds, cloud, ssl, impersonation, code, result }) {
  const sections = [
    {
      id: 'anchorLinker_nds',
      targetId: 'anchorLink_nds',
      title: (
        <span>
          Webサイト
          <span>脆弱性診断</span>
        </span>
      ),
      icon: NdsIcon,
      status: nds.status,
      rank: nds.rank,
      type: 'nds',
      text: NDS_RANKS[nds.rank],
    },
    {
      id: 'anchorLinker_cloud',
      targetId: 'anchorLink_cloud',
      title: (
        <span>
          クラウド利用・
          <span>リスク診断</span>
        </span>
      ),
      icon: CloudIcon,
      status: cloud.status,
      type: 'cloud',
      text: cloud.text,
    },
    {
      id: 'anchorLinker_ssl',
      targetId: 'anchorLink_ssl',
      title: (
        <span>
          実在証明・盗聴防止
          <span>（SSL）診断</span>
        </span>
      ),
      icon: SslIcon,
      status: ssl.status,
      type: 'ssl',
      text: ssl.text,
    },
    {
      id: 'anchorLinker_impersonation',
      targetId: 'anchorLink_impersonation',
      title: 'なりすまし診断',
      icon: ImpersonationIcon,
      status: impersonation.status,
      rank: impersonation.rank,
      type: 'impersonation',
      text: IMPERSONATION_RANKS[impersonation.rank],
    },
  ];

  const handleNewDiagnostic = () => {
    localStorage.setItem('state', JSON.stringify({ diagnosticValue: fqdn }));
    window.open(`${import.meta.env.VITE_PATH_PREFIX}/`, '_blank');
  };
  return (
    <div className="c-overview c-overview--bg">
      <div className="c-overview__head c-overview__head--gray">
        <dl>
          <dt>診断対象のURL</dt>
          <dd>{fqdn}</dd>
        </dl>
        <div className="c-overview__button">
          <Button
            id="another_url"
            onClick={handleNewDiagnostic}
            external
            exIcon="small"
            variant="secondary"
          >
            再度診断する
          </Button>
        </div>
      </div>
      <div className="c-overview__check">
        <SiteRiskPeriodicCheckup
          code={code}
          nextCheckedAt={result?.configuration?.nextCheckedAt}
          isRegularly={result?.configuration?.isRegularly}
          interval={result?.configuration?.interval}
          isNotification={result?.configuration?.isNotification}
          createdAt={result?.overview?.createdAt}
        />
      </div>
      <div className="c-overview__nav c-overview__nav--noAnchor">
        <OverviewGrid>
          {sections.map(section => (
            <OverviewCard
              key={section.id}
              {...section}
              className="c-overviewCard"
              onClick={() => {
                const element = document.getElementById(section.targetId);
                if (element) {
                  element.scrollIntoView({ behavior: 'smooth' });
                }
              }}
            />
          ))}
        </OverviewGrid>
      </div>
      <div className="c-overview__annotation">
        ※各項目の詳細を確認できる期限が過ぎています。
        <span>
          ご確認いただくには
          <Button variant="textXs" onClick={handleNewDiagnostic}>
            再度認診
          </Button>
          を行ってください。
        </span>
      </div>
    </div>
  );
}

ExpiredOverview.propTypes = {
  fqdn: PropTypes.string.isRequired,
  nds: PropTypes.shape({
    status: PropTypes.string.isRequired,
    rank: PropTypes.string.isRequired,
  }).isRequired,
  cloud: PropTypes.shape({
    status: PropTypes.string.isRequired,
    text: PropTypes.string.isRequired,
  }).isRequired,
  ssl: PropTypes.shape({
    status: PropTypes.string.isRequired,
    text: PropTypes.string.isRequired,
  }).isRequired,
  impersonation: PropTypes.shape({
    status: PropTypes.string.isRequired,
    rank: PropTypes.string.isRequired,
  }).isRequired,
  code: PropTypes.string,
  result: PropTypes.shape({
    configuration: PropTypes.shape({
      nextCheckedAt: PropTypes.string,
      isRegularly: PropTypes.bool,
      interval: PropTypes.number,
      isNotification: PropTypes.bool,
    }),
    overview: PropTypes.shape({ createdAt: PropTypes.string }),
  }),
};

export default ExpiredOverview;
