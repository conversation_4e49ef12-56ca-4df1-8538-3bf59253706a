@charset "utf-8";

@use "../global/variables" as *;

/* LP・検索結果表示共通 */
.p-yourbrand {
  background-color: $color-white;
  &__main {
    padding-bottom: 32px;
  }
  &__sec {
    &--gray {
      background-color: $color-gray10;
    }
  }
  &__inner {
    max-width: 1080px;
    margin: 0 auto;
    padding: 100px 20px;
    &--top {
      padding: 144px 20px 100px;
    }
  }
  &__title {
    position: relative;
    display: inline-block;
    font-size: 48px;
    font-weight: 600;
    line-height: 1.4;
    color: $color-black;
    margin-bottom: 60px;
    &::after {
      content: "";
      position: absolute;
      left: 0;
      bottom: -0.35em;
      width: 80px;
      height: 6px;
      background: $color-lightGreen;
    }
  }
  &__titleS {
    font-size: 20px;
  }
  &__button {
    width: 378px;
    margin: 40px auto 0;
  }
  &__note {
    font-size: 12px;
    color: $color-gray50;
    margin-top: 8px;
    max-inline-size: max-content;
    margin-inline: auto;
  }
}
.p-yourbrandMain {
  position: relative;
  display: grid;
  max-width: 1200px;
  margin: 0 auto;
  padding: 146px 20px 64px;
  &__title {
    text-align: center;
    & img {
      width: auto;
      height: 167px;
    }
  }
  &__lead {
    font-size: 20px;
    font-weight: 600;
    text-align: center;
    margin-top: 64px;
  }
  &__search {
    width: 100%;
    max-width: 820px;
    margin: 20px auto 0;
    border: 1px solid $color-gray35;
    border-radius: 40px;
    box-shadow: $shadow-search;
    padding: 16px 0 16px;
    overflow: hidden;
  }
  &__links {
    padding: 16px 16px 0;
  }
  &__error {
    font-size: 12px;
    color: $color-error;
    padding: 0 32px;
  }
}
.p-yourbrandLinks {
  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: max-content;
  column-gap: 8px;
  cursor: pointer;
  &__list {
    font-size: 14px;
    text-align: center;
    color: $color-gray70;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    padding: 4px 16px;
    border: 1px solid $color-gray35;
    border-radius: $border-radius-round;
    @media (min-width: $window_tb_min) {
      &:hover {
        color: $color-green;
        border-color: transparent;
        background-color: $color-lightGreen-alpha20;
        & span {
          color: $color-green;
        }
      }
    }
    & span {
      margin-right: 4px;
    }
    &--active {
      color: $color-green;
      background-color: $color-lightGreen-alpha20;
      border: 1px solid transparent;
    }
    &--arrow {
      position: relative;
      padding: 4px 22px 4px 16px;
      &::before {
        content: "";
        position: absolute;
        top: 0;
        bottom: 0;
        right: 12px;
        width: 6px;
        height: 6px;
        margin: auto;
        border-top: 1px solid $color-gray70;
        border-right: 1px solid $color-gray70;
        transform: rotate(45deg);
      }
      &:hover {
        text-decoration: none;
        &::before {
          border-top: 1px solid $color-green;
          border-right: 1px solid $color-green;
        }
      }
    }
  }
}
.p-yourbrandCorpLogo {
  display: flex;
  flex-direction: column;
  row-gap: 32px;
  overflow: hidden;
  &__lead {
    display: flex;
    justify-content: center;
    align-items: center;
    column-gap: 24px;
    font-size: 28px;
    font-weight: 600;
  }
  &__leadImg {
    & img {
      width: auto;
      height: 48px;
    }
  }
  &__text {
    font-size: 16px;
    text-align: center;
  }
}
.p-yourbrandCorpLogoBelt {
  display: inline-flex;
  animation: animation-beltScroll 18s linear infinite;
  & li {
    min-width: 128px;
    flex: 0 0 auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    row-gap: 8px;
    margin-left: 40px;
    & img {
      width: auto;
      height: 46px;
    }
    & span {
      font-size: 20px;
      font-weight: 600;
    }
  }
}
.p-yourbrandColumn {
  display: grid;
  grid-template-columns: repeat(2, calc(50% - 16px));
  gap: 32px;
  &__title {
    position: relative;
    display: inline-block;
    font-size: 48px;
    font-weight: 600;
    line-height: 1.4;
    color: $color-black;
    &::after {
      content: "";
      position: absolute;
      left: 0;
      bottom: -0.35em;
      width: 80px;
      height: 6px;
      background: $color-lightGreen;
    }
  }
  &__text {
    font-size: 16px;
  }
}
.p-yourbrandMerit {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  & li {
    display: grid;
  }
}
.p-yourbrandMeritCard {
  display: grid;
  background-color: $color-white;
  border: 1px solid $color-gray20;
  border-radius: $border-radius-card;
  padding: 20px 20px 40px;
  &__order {
    font-size: 46px;
    font-weight: 600;
    color: $color-gray20;
  }
  &__title {
    font-size: 24px;
    font-weight: 600;
    text-align: center;
    color: $color-green;
    margin-bottom: 20px;
  }
  &__text {
    font-size: 16px;
    margin-top: auto;
  }
}
.p-yourbrandTable {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
  background: $color-white;
  th {
    border: 1px solid $color-gray20;
    padding: 20px;
    vertical-align: middle;
  }
  td {
    border: 1px solid $color-gray20;
    padding: 20px;
    vertical-align: middle;
  }
  & sup {
    font-size: 10px;
  }
  &__sectionHeader {
    font-size: 20px;
    background: $color-darkGreen;
    color: $color-white;
    font-weight: 600;
    text-align: left;
    padding-left: 20px;
  }
  &__feature {
    font-size: 20px;
    text-align: left;
    height: 104px;
  }
  &__check {
    font-size: 40px;
    text-align: center;
    line-height: 1;
  }
  &__planName {
    display: block;
    font-size: 24px;
    font-weight: 600;
    color: $color-darkGreen;
    text-align: center;
    padding: 16px 20px 8px;
    border-top: 1px solid $color-gray20;
    border-left: 1px solid $color-gray20;
  }
  &__planText {
    display: block;
    padding: 0 20px 20px;
    border-left: 1px solid $color-gray20;
  }
  &__contact {
    & td {
      font-size: 20px;
      text-align: center;
    }
  }
  &__note {
    font-size: 12px;
    line-height: 1.4;
    color: $color-gray50;
    margin-top: 20px;
  }
  &__price {
    font-size: 20px;
  }
  /* プランテーブル用 */
  &__badge {
    width: 100%;
    font-size: 20px;
    font-weight: 600;
    display: block;
    background: $color-darkGreen;
    color: $color-white;
    text-align: center;
    padding: 4px 0;
    min-height: 40px;
    &--null {
      background: $color-gray10;
    }
  }
  &--plan {
    thead th {
      font-size: 16px;
      text-align: left;
      font-weight: 300;
      padding: 0;
      vertical-align: top;
      border: 0;
      &:first-child {
        background-color: $color-gray10;
      }
      &:last-child {
        & .p-yourbrandTable__planName,
        .p-yourbrandTable__planText {
          border-right: 1px solid $color-gray20;
        }
      }
    }
    th:nth-child(3),
    tbody td:nth-child(3) {
      background: $color-lightGreen10;
    }
  }
  /* 価格テーブル用 */
  &--cost {
    table-layout: auto;
    & .p-yourbrandTable__feature {
      width: 40%;
    }
    & .p-yourbrandTable__price {
      width: 60%;
      text-align: center;
    }
  }
}
.p-yourbrandCorpEx {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
  gap: 10px;
  &__item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    background: $color-white;
    padding: 20px;
    row-gap: 16px;
  }
  &__logo {
    width: 200px;
  }
  &__company {
    font-size: 16px;
    text-align: center;
  }
  &__tld {
    width: 100%;
    font-size: 24px;
    font-weight: 600;
    padding-top: 12px;
    border-top: 1px solid $color-gray20;
  }
}

/* 検索結果申請フロー */
.p-yourbrandFlow {
  display: grid;
  gap: 10px;
  &__list {
    display: grid;
    grid-template-columns: 120px 1fr;
    gap: 24px;
  }
  &__cardWrap {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  &__timeline {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-left: 16px;
    border-left: 4px solid $color-gray20;
    height: 100%;
    text-align: center;
    font-size: 16px;
    & span {
      margin-top: 16px;
    }
  }
  &__card {
    display: grid;
    grid-template-columns: 48px 1fr;
    align-items: center;
    border: 1px solid $color-gray20;
    border-radius: 8px;
    padding: 20px 24px;
    gap: 16px;
  }
  &__num {
    font-size: 46px;
    font-weight: 600;
    color: $color-gray20;
  }
  &__heading {
    font-size: 24px;
    font-weight: 600;
    color: $color-green;
  }
  &__body {
    margin-top: 8px;
    word-break: break-all;
  }
}

/* margin */
.p-yourbrandColumn__title + .p-yourbrandColumn__text {
  margin-top: 32px;
}
.p-yourbrandColumn + .p-yourbrandColumn {
  margin-top: 96px;
}

/* sp style */
@media (min-width: 1px) and (max-width: $window_sp_max) {
  .p-yourbrand {
    &__inner {
      padding: 60px 16px 60px;
      &--top {
        padding: 124px 16px 60px;
      }
    }
    &__title {
      font-size: 38px;
    }
    &__titleBreak {
      display: block;
    }
    &__table {
      &--scroll {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        /* innerの右横paddingをscroll tableだけoff */
        margin-right: -16px;
        padding-right: 16px;
        & table {
          /* iPhone用 max-contentのためのinline-block */
          display: inline-block;
          width: 100%;
          min-width: max-content;
        }
      }
    }
    &__button {
      width: 100%;
    }
  }
  .p-yourbrandMain {
    position: relative;
    padding: 184px 16px 48px;
    &__title {
      & img {
        height: 120px;
      }
    }
    &__lead {
      font-size: 16px;
      margin-top: 48px;
      & span {
        display: block;
      }
    }
    &__search {
      margin-top: 24px;
      padding: 12px 0 16px;
    }
    &__links {
      max-width: 100%;
      padding: 12px 16px 0;
    }
    &__error {
      padding: 0 20px;
    }
  }
  .p-yourbrandLinks {
    column-gap: 4px;
    &__list {
      font-size: 12px;
    }
  }
  .p-yourbrandCorpLogo {
    &__lead {
      flex-direction: column;
      row-gap: 16px;
      font-size: 20px;
    }
    &__text {
      font-size: 12px;
    }
  }
  .p-yourbrandCorpLogoBelt {
    & li {
      min-width: 80px;
      margin-left: 16px;
      & img {
        width: auto;
        height: 28px;
      }
      & span {
        font-size: 12px;
      }
    }
  }
  .p-yourbrandColumn {
    display: flex;
    flex-direction: column-reverse;
    &:nth-child(2) {
      flex-direction: column;
    }
    &__title {
      font-size: 38px;
    }
  }
  .p-yourbrandMerit {
    display: flex;
    flex-direction: column;
  }
  .p-yourbrandTable {
    th {
      padding: 20px 10px;
    }
    td {
      padding: 20px 10px;
    }
    &--plan {
      thead th {
        max-width: 246px;
      }
    }
    &__sectionHeader {
      font-size: 16px;
    }
    &__feature {
      font-size: 16px;
    }
    &__check {
      font-size: 32px;
    }
    &__contact {
      & td {
        font-size: 16px;
      }
    }
    &__price {
      font-size: 16px;
      & span {
        display: block;
      }
    }
  }
  .p-yourbrandCorpEx {
    grid-template-columns: repeat(2, 1fr);
    gap: 5px;
    &__tld {
      font-size: 20px;
      margin-top: auto;
    }
  }
  /* 検索結果申請フロー */
  .p-yourbrandFlow {
    &__list {
      grid-template-columns: 60px 1fr;
      gap: 16px;
    }
    &__timeline {
      flex-direction: row;
      writing-mode: vertical-lr;
      padding-left: 8px;
      letter-spacing: 10px;
    }
    &__card {
      grid-template-columns: 36px 1fr;
    }
    &__num {
      font-size: 32px;
    }
    &__heading {
      font-size: 20px;
    }
  }
  /* margin */
  .p-yourbrandColumn + .p-yourbrandColumn {
    margin-top: 80px;
  }
}
