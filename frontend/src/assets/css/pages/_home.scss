@charset "utf-8";

@use "../global/variables" as *;

.p-home {
  background: $color-white;
  &__inner {
    position: relative;
    max-width: 1200px;
    margin: 0 auto;
    padding: 184px 20px 40px;
  }
  &__header {
    & img {
      width: auto;
      height: 129px;
    }
  }
  &__text {
    font-size: 20px;
    font-weight: 600;
    margin-inline: auto;
    max-inline-size: max-content;
    margin-top: 64px;
  }
  &__tabs {
    padding: 16px 16px 0;
  }
  &__content {
    transition: opacity 0.3s;
    opacity: 0;
    &--active {
      transition: opacity 0.3s;
      opacity: 1;
    }
  }
  &__link {
    font-size: 12px;
    color: $color-gray50;
    margin-inline: auto;
    max-inline-size: max-content;
    & a {
      font-size: inherit;
      color: inherit;
      text-decoration: underline;
      transition: color 0.3s;
      @media (min-width: $window_tb_min) {
        &:hover {
          color: $color-lightGreen;
        }
      }
    }
  }
  &__error {
    font-size: 12px;
    color: $color-error;
    padding: 0 32px;
  }
  &__search {
    max-width: 820px;
    margin: 20px auto 0;
    border: 1px solid $color-gray35;
    border-radius: 40px;
    box-shadow: $shadow-search;
    padding: 16px 0 16px;
    overflow: hidden;
  }
  &__brandTld {
    width: 482px;
    margin: 0 auto;
    padding: 2px;
    background: linear-gradient(
      90deg,
      $color-lightGreen 0%,
      $color-green 62%,
      $color-lightGreen 100%
    );
    border-radius: $border-radius-round;
    @media (min-width: $window_tb_min) {
      &:hover {
        color: $color-green;
        background: linear-gradient(
          90deg,
          $color-green 0%,
          $color-lightGreen 62%,
          $color-green 100%
        );
      }
    }
  }
  &__brandLink {
    width: 478px;
    position: relative;
    display: inline-block;
    font-size: 16px;
    padding: 6px 24px;
    color: $color-green;
    background-color: $color-white;
    border-radius: $border-radius-round;
    &::before {
      content: "";
      position: absolute;
      top: 0;
      bottom: 0;
      right: 16px;
      width: 8px;
      height: 8px;
      margin: auto;
      border-top: 1px solid $color-green;
      border-right: 1px solid $color-green;
      transform: rotate(45deg);
    }
    @media (min-width: $window_tb_min) {
      &:hover {
        color: $color-green;
        text-decoration: none;
        background: $color-lightGreen10;
      }
      &:visited {
        color: $color-green;
        text-decoration: none;
      }
    }
  }
  &__textEm {
    font-weight: 600;
  }
}
.p-homeHeader {
  text-align: center;
}
.p-homeTabs {
  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: max-content;
  column-gap: 8px;
  cursor: pointer;
  &__list {
    font-family: inherit;
    font-size: 14px;
    text-align: center;
    color: $color-gray70;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    padding: 4px 16px;
    border: 1px solid $color-gray35;
    border-radius: $border-radius-round;
    @media (min-width: $window_tb_min) {
      &:hover {
        color: $color-green;
        border-color: transparent;
        background-color: $color-lightGreen-alpha20;
        & span {
          color: $color-green;
        }
      }
    }
    & span {
      margin-right: 4px;
    }
    &--active {
      color: $color-green;
      background-color: $color-lightGreen-alpha20;
      border: 1px solid transparent;
    }
    &--arrow {
      position: relative;
      padding: 4px 22px 4px 16px;
      &::before {
        content: "";
        position: absolute;
        top: 0;
        bottom: 0;
        right: 12px;
        width: 6px;
        height: 6px;
        margin: auto;
        border-top: 1px solid $color-gray70;
        border-right: 1px solid $color-gray70;
        transform: rotate(45deg);
      }
      &:hover {
        text-decoration: none;
        &::before {
          border-top: 1px solid $color-green;
          border-right: 1px solid $color-green;
        }
      }
    }
  }
}
/* margin */
.p-home__search + .p-home__brandTld {
  margin-top: 24px;
}
.p-home__brandTld + .p-home__link {
  margin-top: 120px;
}

/* sp style */
@media (min-width: 1px) and (max-width: $window_sp_max) {
  .p-home {
    &__inner {
      max-width: 100%;
      padding: 184px 16px 40px;
    }
    &__header {
      & img {
        height: 76px;
      }
    }
    &__tabs {
      max-width: 100%;
      padding: 12px 16px 0;
    }
    &__text {
      font-size: 16px;
      text-align: center;
      margin-top: 92px;
      & span {
        display: block;
      }
    }
    &__error {
      padding: 0 20px;
    }
    &__search {
      margin-top: 24px;
      padding: 12px 0 16px;
    }
    &__brandTld {
      width: 100%;
      padding: 2px;
    }
    &__brandLink {
      width: 100%;
      font-weight: 300;
      text-align: center;
    }
    &__textBreak {
      display: block;
    }
  }
  .p-homeTabs {
    column-gap: 4px;
    &__list {
      font-size: 12px;
    }
  }
  /* margin */
  .p-home__search + .p-home__brandTld {
    margin-top: 64px;
  }
  .p-home__brandTld + .p-home__link {
    margin-top: 64px;
  }
}
