@charset "utf-8";

@use "../global/variables" as *;

/* ボタンmixin */
@mixin button-base($border-radius: $border-radius-button) {
  position: relative;
  font-family: inherit;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  column-gap: 8px;
  max-width: 100%;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 2px solid $color-white;
    background-color: $color-white;
    border-radius: $border-radius;
    opacity: 0;
    transition: opacity 0.3s;
  }
  &[disabled] {
    background: $color-gray20;
    box-shadow: none;
    pointer-events: none;
  }
}
/* ボタンスタイルmixin */
@mixin button-variant($color-text, $color-bg, $border-radius) {
  color: $color-text;
  background: $color-bg;
  border-radius: $border-radius;
}
/* ボタンhoverスタイルmixin */
@mixin button-hover {
  @media (min-width: $window_tb_min) {
    &:not([disabled]):hover {
      &::before {
        content: "";
        opacity: 0.3;
      }
    }
  }
}

/* メイン */
.c-buttonPrimary {
  @include button-base;
  @include button-variant(
    $color-black,
    $color-lightGreen,
    $border-radius-button
  );
  box-shadow: $shadow-10-green;
  font-size: 14px;
  min-height: 55px;
  padding-left: 20px;
  padding-right: 20px;
  text-align: center;
  position: relative;
  & .icon-sec-link {
    position: absolute;
    top: 50%;
    right: 12px;
    transform: translateY(-50%);
  }
  @include button-hover;
}
/* 背景深緑グラデ ボタン */
.c-buttonAccent {
  @include button-base;
  @include button-variant(
    $color-white,
    linear-gradient(180deg, #13d4a4 0%, #0b4d4b 50%) no-repeat,
    8px
  );
  font-size: 20px;
  border: 0;
  min-height: 56px;
  padding-top: 6px;
  padding-bottom: 6px;
  padding-left: 20px;
  padding-right: 20px;
  text-align: center;
  position: relative;
  & .icon-sec-link {
    position: absolute;
    top: 50%;
    right: 16px;
    transform: translateY(-50%);
  }
  @media (min-width: $window_tb_min) {
    &:not([disabled]):hover {
      background: linear-gradient(180deg, #13d4a4 0%, #0b4d4b 90%) no-repeat;
    }
  }
}

/* 背景白 outlineボタン */
.c-buttonSecondary {
  @include button-base;
  @include button-variant($color-gray80, $color-white, $border-radius-button);
  font-size: 12px;
  min-height: 32px;
  padding-left: 10px;
  padding-right: 10px;
  border: 1px solid $color-gray20;
  @include button-hover;
}
/* 背景白 outlineボタン aタグのhoverケース */
a.c-buttonSecondary:hover {
  color: $color-gray80;
  text-decoration: none;
  @include button-hover;
}

/* 検索ボタン */
.c-buttonSearch {
  @include button-base($border-radius-round);
  @include button-variant(
    $color-black,
    $color-lightGreen,
    $border-radius-round
  );
  font-size: 16px;
  min-width: 126px;
  min-height: 50px;
  @include button-hover;
}
/* chatボタン */
.c-buttonChat {
  @include button-base($border-radius-round);
  @include button-variant($color-darkGreen, $color-white, $border-radius-round);
  padding-left: 20px;
  padding-right: 20px;
  min-height: 50px;
  border: 2px solid $color-darkGreen;
  box-shadow: $shadow-20;
  &::before {
    border-radius: 50px;
  }
  @include button-hover;
}
/* 画像ボタン */
.c-buttonImage {
  transition: opacity 0.3s;
  @media (min-width: $window_tb_min) {
    &:not([disabled]):hover {
      opacity: 0.8;
    }
  }
}
/* 貴社名メインボタン */
.c-buttonTldPrimary {
  @include button-base;
  @include button-variant(
    $color-black,
    $color-lightGreen,
    $border-radius-round
  );
  box-shadow: $shadow-10-green;
  font-size: 14px;
  min-height: 55px;
  padding-left: 20px;
  padding-right: 20px;
  text-align: center;
  position: relative;
  & .icon-sec-link {
    position: absolute;
    top: 50%;
    right: 12px;
    transform: translateY(-50%);
  }
  @include button-hover;
}
/* 貴社名 LP用ボタン */
.c-buttonTldAccent {
  @include button-base($border-radius-round);
  @include button-variant(
    $color-black,
    $color-lightGreen,
    $border-radius-round
  );
  font-size: 18px;
  padding-left: 20px;
  padding-right: 20px;
  min-height: 63px;
  @include button-hover;
}
/* テキストリンク アイコンのみボダン */
.c-buttonText {
  font-family: inherit;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: $color-darkGreen;
  column-gap: 4px;
  &--xs {
    font-size: 10px;
  }
  &--inline {
    display: inline;
  }
  &--underline {
    color: $color-gray80;
    font-size: 12px;
    position: relative;
    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 0;
      width: calc(100% - 12px);
      height: 1px;
      background-color: $color-gray80;
      transition: transform 0.3s;
    }
  }
  @media (min-width: $window_tb_min) {
    &:not([disabled]):hover {
      opacity: 0.5;
    }
  }
}

/* width サイズ */
.c-button--sizeFull {
  width: 100%;
}

/* コピーボタン */
.c-buttonCopy {
  position: relative;
  &__message {
    position: absolute;
    top: -40px;
    right: -40px;
    width: 110px;
    font-size: 12px;
    text-align: center;
    background-color: $color-darkGreen;
    color: $color-white;
    border-radius: $border-radius-card;
    padding: 4px 8px;
    animation: animation-fadeIn 0.3s;
    display: none;
  }
}

/* sp style */
@media (min-width: 1px) and (max-width: $window_sp_max) {
  .c-buttonSearch {
    column-gap: 4px;
    font-size: 12px;
    min-width: 46px;
    min-height: 46px;
  }
}
