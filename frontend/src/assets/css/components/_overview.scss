@charset "utf-8";

@use "../global/variables" as *;

/* ExpiredOverviewでも共通利用しているので注意 */

.c-overview {
  &--bg {
    background-color: $color-white;
    border-radius: $border-radius-card;
    padding-top: 20px;
  }
  &__head {
    display: grid;
    grid-template-columns: 1fr auto;
    align-items: center;
    background-color: $color-white;
    padding: 20px;
    &--gray {
      background-color: $color-gray10;
      border-radius: $border-radius-card;
      margin-left: 20px;
      margin-right: 20px;
    }
    & dt {
      font-size: 12px;
    }
    & dd {
      font-size: 24px;
      font-weight: 600;
      & span {
        display: block;
        font-size: 12px;
        font-weight: 300;
        color: $color-gray80;
        margin-top: 4px;
      }
    }
  }
  &__check {
    background-color: $color-white;
    padding: 0 20px 20px 20px;
    border-radius: 0 0$border-radius-card $border-radius-card;
  }
  &__note {
    display: grid;
    grid-template-columns: auto 1fr;
    align-items: flex-start;
    column-gap: 8px;
    color: $color-white;
    border-radius: $border-radius-card $border-radius-card 0 0;
    background-color: $color-green;
    padding: 8px 20px;
    & span {
      margin-top: 4px;
    }
    &--error {
      background-color: $color-error;
    }
    &--warning {
      background-color: $color-orange;
    }
  }
  &__nav {
    margin-top: 20px;
    &--noAnchor {
      & .c-overviewGrid {
        gap: 0;
      }
      & .c-overviewCard {
        position: relative;
        box-shadow: none;
        pointer-events: none;
      }
      & .c-overviewCard + .c-overviewCard::before {
        content: "";
        height: 92px;
        position: absolute;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
        border-left: 1px solid $color-gray20;
      }
    }
  }
  &__annotation {
    font-size: 10px;
    color: $color-gray80;
    text-align: center;
    padding: 10px 20px 20px 20px;
  }
}
.c-overviewGrid {
  display: flex;
  column-gap: 10px;
}
.c-overviewCard {
  width: 100%;
  display: grid;
  grid-template-rows: subgrid;
  row-gap: 16px;
  background-color: $color-white;
  border-radius: $border-radius-card;
  padding: 20px;
  box-shadow: $shadow-10;
  transition: opacity 0.3s;
  &--tab {
    box-shadow: none;
    border-radius: $border-radius-card $border-radius-card 0 0;
  }
  &__title {
    display: flex;
    column-gap: 8px;
    font-weight: 600;
    grid-row: span 4;
    & span {
      text-align: left;
    }
  }
  &__detail {
    display: grid;
    row-gap: 12px;
  }
  &__icon {
    display: flex;
    justify-content: center;
    align-self: center;
    grid-row: span 6;
    & img {
      width: 60px;
      height: auto;
    }
  }
  &__text {
    font-size: 12px;
    grid-row: span 2;
  }
  &__badge {
    display: inline-block;
    font-size: 14px;
    color: $color-white;
    background-color: $color-green;
    padding: 4px 20px;
    border-radius: $border-radius-round;
    &--warning {
      background-color: $color-orange;
    }
    &--C {
      background-color: $color-orange;
    }
    &--D {
      background-color: $color-orange;
    }
    &--impB {
      background-color: $color-orange;
    }
    &--alert {
      background-color: $color-error;
    }
    &--E {
      background-color: $color-error;
    }
    &--impC {
      background-color: $color-error;
    }
    &--impD {
      background-color: $color-error;
    }
  }
}
/* margin */
.c-overview__head--gray + .c-overview__check {
  margin-top: 20px;
}
/* sp style */
@media (min-width: 1px) and (max-width: $window_sp_max) {
  .c-overview {
    &__head {
      grid-template-columns: inherit;
      row-gap: 10px;
      justify-content: flex-start;
      &--gray {
        margin-left: 10px;
        margin-right: 10px;
      }
      & dd {
        font-size: 16px;
      }
    }
    &__note {
      font-size: 15px;
    }
    &__nav {
      &--noAnchor {
        & .c-overviewCard {
          width: calc(100% / 2);
        }
        & .c-overviewCard + .c-overviewCard::before {
          border: 0;
        }
        & .c-overviewCard:nth-child(2n)::before {
          content: "";
          height: 122px;
          position: absolute;
          top: 50%;
          left: 0;
          transform: translateY(-50%);
          border-left: 1px solid $color-gray20;
        }
      }
    }
    &__annotation {
      & span {
        display: block;
      }
    }
  }
  .c-overviewGrid {
    flex-wrap: wrap;
    row-gap: 10px;
    &--tab {
      margin-bottom: 10px;
    }
  }
  .c-overviewCard {
    width: calc((100% - 10px) / 2);
    padding: 20px 12px;
    row-gap: 10px;
    &--tab {
      padding: 10px;
      border-radius: $border-radius-card;
      & .c-overviewCard__title {
        flex-direction: row;
        font-size: 12px;
        font-weight: 300;
        grid-row: span 2;
        & .icon-size20 {
          font-size: 16px;
        }
      }
      & .c-overviewCard__detail {
        display: flex;
        align-items: center;
        column-gap: 10px;
      }
      & .c-overviewCard__icon {
        width: 32px;
      }
      & .c-overviewCard__icon img {
        width: 32px;
        height: 32px;
      }
      & .c-overviewCard__badge {
        width: 100%;
        font-size: 10px;
        padding: 2px 8px;
      }
      & .c-overviewCard__text {
        display: block;
        font-size: 10px;
        flex: 1 0 0%;
      }
      & .c-loader {
        width: 20px;
        height: 20px;
        &::before {
          width: 12px;
          height: 12px;
        }
      }
    }
    &__title {
      flex-direction: column;
      row-gap: 8px;
      grid-row: span 8;
      & .icon-base {
        display: block;
        text-align: center;
      }
    }
    &__icon {
      grid-row: span 8;
    }
    &__text {
      display: flex;
      justify-self: center;
      align-items: center;
      grid-row: span 4;
    }
  }
}
