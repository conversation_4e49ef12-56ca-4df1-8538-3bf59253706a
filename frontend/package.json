{"name": "test-github-action-frontend", "version": "1.0.0", "description": "Frontend application for test GitHub Action", "main": "src/Main.js", "type": "module", "license": "MIT", "packageManager": "yarn@4.9.1", "scripts": {"dev": "vite", "build": "vite build", "dev-build": "vite build", "preview": "vite preview", "lint": "eslint ."}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@eslint/js": "^9.17.0", "@vitejs/plugin-react": "^4.3.3", "eslint": "^9.17.0", "globals": "^15.12.0", "vite": "^5.4.10"}}